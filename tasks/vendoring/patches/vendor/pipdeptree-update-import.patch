--- a/pipenv/vendor/pipdeptree/__main__.py	2024-10-12 19:43:30.971617798 +0200
+++ b/pipenv/vendor/pipdeptree/__main__.py	2024-10-12 20:20:58.508248189 +0200
@@ -2,9 +2,16 @@

 from __future__ import annotations
 
+import os
 import sys
 from typing import Sequence

+pardir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
+# for finding pipdeptree itself
+sys.path.append(pardir)
+# for finding stuff in vendor and patched
+sys.path.append(os.path.dirname(os.path.dirname(pardir)))
+
 from pipenv.vendor.pipdeptree._cli import get_options
 from pipenv.vendor.pipdeptree._detect_env import detect_active_interpreter
 from pipenv.vendor.pipdeptree._discovery import get_installed_distributions
