diff --git a/pipenv/patched/pip/_internal/commands/__init__.py b/pipenv/patched/pip/_internal/commands/__init__.py
index bc4f216a8..156846015 100644
--- a/pipenv/patched/pip/_internal/commands/__init__.py
+++ b/pipenv/patched/pip/_internal/commands/__init__.py
@@ -14,97 +14,97 @@ CommandInfo = namedtuple("CommandInfo", "module_path, class_name, summary")
 # - Enables avoiding additional (costly) imports for presenting `--help`.
 # - The ordering matters for help display.
 #
-# Even though the module path starts with the same "pip._internal.commands"
+# Even though the module path starts with the same "pipenv.patched.pip._internal.commands"
 # prefix, the full path makes testing easier (specifically when modifying
 # `commands_dict` in test setup / teardown).
 commands_dict: Dict[str, CommandInfo] = {
     "install": CommandInfo(
-        "pip._internal.commands.install",
+        "pipenv.patched.pip._internal.commands.install",
         "InstallCommand",
         "Install packages.",
     ),
     "lock": CommandInfo(
-        "pip._internal.commands.lock",
+        "pipenv.patched.pip._internal.commands.lock",
         "LockCommand",
         "Generate a lock file.",
     ),
     "download": CommandInfo(
-        "pip._internal.commands.download",
+        "pipenv.patched.pip._internal.commands.download",
         "DownloadCommand",
         "Download packages.",
     ),
     "uninstall": CommandInfo(
-        "pip._internal.commands.uninstall",
+        "pipenv.patched.pip._internal.commands.uninstall",
         "UninstallCommand",
         "Uninstall packages.",
     ),
     "freeze": CommandInfo(
-        "pip._internal.commands.freeze",
+        "pipenv.patched.pip._internal.commands.freeze",
         "FreezeCommand",
         "Output installed packages in requirements format.",
     ),
     "inspect": CommandInfo(
-        "pip._internal.commands.inspect",
+        "pipenv.patched.pip._internal.commands.inspect",
         "InspectCommand",
         "Inspect the python environment.",
     ),
     "list": CommandInfo(
-        "pip._internal.commands.list",
+        "pipenv.patched.pip._internal.commands.list",
         "ListCommand",
         "List installed packages.",
     ),
     "show": CommandInfo(
-        "pip._internal.commands.show",
+        "pipenv.patched.pip._internal.commands.show",
         "ShowCommand",
         "Show information about installed packages.",
     ),
     "check": CommandInfo(
-        "pip._internal.commands.check",
+        "pipenv.patched.pip._internal.commands.check",
         "CheckCommand",
         "Verify installed packages have compatible dependencies.",
     ),
     "config": CommandInfo(
-        "pip._internal.commands.configuration",
+        "pipenv.patched.pip._internal.commands.configuration",
         "ConfigurationCommand",
         "Manage local and global configuration.",
     ),
     "search": CommandInfo(
-        "pip._internal.commands.search",
+        "pipenv.patched.pip._internal.commands.search",
         "SearchCommand",
         "Search PyPI for packages.",
     ),
     "cache": CommandInfo(
-        "pip._internal.commands.cache",
+        "pipenv.patched.pip._internal.commands.cache",
         "CacheCommand",
         "Inspect and manage pip's wheel cache.",
     ),
     "index": CommandInfo(
-        "pip._internal.commands.index",
+        "pipenv.patched.pip._internal.commands.index",
         "IndexCommand",
         "Inspect information available from package indexes.",
     ),
     "wheel": CommandInfo(
-        "pip._internal.commands.wheel",
+        "pipenv.patched.pip._internal.commands.wheel",
         "WheelCommand",
         "Build wheels from your requirements.",
     ),
     "hash": CommandInfo(
-        "pip._internal.commands.hash",
+        "pipenv.patched.pip._internal.commands.hash",
         "HashCommand",
         "Compute hashes of package archives.",
     ),
     "completion": CommandInfo(
-        "pip._internal.commands.completion",
+        "pipenv.patched.pip._internal.commands.completion",
         "CompletionCommand",
         "A helper command used for command completion.",
     ),
     "debug": CommandInfo(
-        "pip._internal.commands.debug",
+        "pipenv.patched.pip._internal.commands.debug",
         "DebugCommand",
         "Show information useful for debugging.",
     ),
     "help": CommandInfo(
-        "pip._internal.commands.help",
+        "pipenv.patched.pip._internal.commands.help",
         "HelpCommand",
         "Show help for commands.",
     ),
diff --git a/pipenv/patched/pip/_vendor/requests/packages.py b/pipenv/patched/pip/_vendor/requests/packages.py
index 200c38287..c92ef2d0d 100644
--- a/pipenv/patched/pip/_vendor/requests/packages.py
+++ b/pipenv/patched/pip/_vendor/requests/packages.py
@@ -6,14 +6,14 @@ from .compat import chardet
 # I don't like it either. Just look the other way. :)

 for package in ("urllib3", "idna"):
-    vendored_package = "pip._vendor." + package
+    vendored_package = "pipenv.patched.pip._vendor." + package
     locals()[package] = __import__(vendored_package)
     # This traversal is apparently necessary such that the identities are
     # preserved (requests.packages.urllib3.* is urllib3.*)
     for mod in list(sys.modules):
         if mod == vendored_package or mod.startswith(vendored_package + '.'):
-            unprefixed_mod = mod[len("pip._vendor."):]
-            sys.modules['pip._vendor.requests.packages.' + unprefixed_mod] = sys.modules[mod]
+            unprefixed_mod = mod[len("pipenv.patched.pip._vendor."):]
+            sys.modules['pipenv.patched.pip._vendor.requests.packages.' + unprefixed_mod] = sys.modules[mod]

 if chardet is not None:
     target = chardet.__name__
