diff --git a/pipenv/patched/pip/_internal/index/collector.py b/pipenv/patched/pip/_internal/index/collector.py
index 5f8fdee3d..b7c40c190 100644
--- a/pipenv/patched/pip/_internal/index/collector.py
+++ b/pipenv/patched/pip/_internal/index/collector.py
@@ -399,9 +399,11 @@ class LinkCollector:
         self,
         session: PipSession,
         search_scope: SearchScope,
+        index_lookup: Optional[Dict[str, List[str]]] = None,
     ) -> None:
         self.search_scope = search_scope
         self.session = session
+        self.index_lookup = index_lookup if index_lookup else {}

     @classmethod
     def create(
@@ -409,6 +411,7 @@ class LinkCollector:
         session: PipSession,
         options: Values,
         suppress_no_index: bool = False,
+        index_lookup: Optional[Dict[str, List[str]]] = None,
     ) -> "LinkCollector":
         """
         :param session: The Session to use to make requests.
@@ -430,10 +433,12 @@ class LinkCollector:
             find_links=find_links,
             index_urls=index_urls,
             no_index=options.no_index,
+            index_lookup=index_lookup,
         )
         link_collector = LinkCollector(
             session=session,
             search_scope=search_scope,
+            index_lookup=index_lookup,
         )
         return link_collector

diff --git a/pipenv/patched/pip/_internal/models/search_scope.py b/pipenv/patched/pip/_internal/models/search_scope.py
index ee7bc8622..5e24ae67e 100644
--- a/pipenv/patched/pip/_internal/models/search_scope.py
+++ b/pipenv/patched/pip/_internal/models/search_scope.py
@@ -4,7 +4,7 @@ import os
 import posixpath
 import urllib.parse
 from dataclasses import dataclass
-from typing import List
+from typing import Dict, List, Optional

 from pip._vendor.packaging.utils import canonicalize_name

@@ -15,17 +15,16 @@ from pip._internal.utils.misc import normalize_path, redact_auth_from_url
 logger = logging.getLogger(__name__)


-@dataclass(frozen=True)
+@dataclass(frozen=False)
 class SearchScope:
     """
     Encapsulates the locations that pip is configured to search.
     """
-
-    __slots__ = ["find_links", "index_urls", "no_index"]
-
     find_links: List[str]
     index_urls: List[str]
     no_index: bool
+    index_lookup: Optional[Dict[str, str]] = None
+    index_restricted: Optional[bool] = None

     @classmethod
     def create(
@@ -33,6 +32,8 @@ class SearchScope:
         find_links: List[str],
         index_urls: List[str],
         no_index: bool,
+        index_lookup: Optional[Dict[str, List[str]]] = None,
+        index_restricted: bool = False,
     ) -> "SearchScope":
         """
         Create a SearchScope object after normalizing the `find_links`.
@@ -67,6 +68,8 @@ class SearchScope:
             find_links=built_find_links,
             index_urls=index_urls,
             no_index=no_index,
+            index_lookup=index_lookup or {},
+            index_restricted=index_restricted,
         )

     def get_formatted_locations(self) -> str:
@@ -124,4 +127,9 @@ class SearchScope:
                 loc = loc + "/"
             return loc

-        return [mkurl_pypi_url(url) for url in self.index_urls]
+        index_urls = self.index_urls
+        if project_name in self.index_lookup:
+            index_urls = [self.index_lookup[project_name]]
+        elif self.index_restricted and self.index_urls:
+            index_urls = [self.index_urls[0]]
+        return [mkurl_pypi_url(url) for url in index_urls]
