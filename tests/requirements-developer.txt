# This is the pip requirements file for developers and release managers. This
# will install development tools and release helpers as well as the
# requirements for running the PyInstaller base test-suite.
#
# Example (assuming current dir is PyInstaller's top-level source dir)::
#
#   python -m pip install -r tests/requirements-developer.txt
#   python -m pip install -r tests/requirements-libraries.txt  # extensive

# include PyInstaller's requirements and for base testing
-r ../requirements.txt
-r requirements-tools.txt

### Helpers for development

ipython  # Better interactive Python shell.
pyreadline ; sys_platform == 'win32'  # Colors in IPython, Windows-only package.
pycmd  # Contains 'py.cleanup' that removes all .pyc files and similar.

### Helpers for releases

wheel>0.24.0  # For creating .whl packages
twine         # For secure upload of tar.gz to PYPI.

towncrier     # For creating the change-log file.

zest.releaser>=6.13.1 # Makes releasing easier
# Addons for zest.releaser:
check-manifest  # Checks MANIFEST.in
pyroma   # Checks if package follows best practices of Python packaging.
chardet  # character encoding detector.
readme   # Check PyPI description is valid reStructuredText
zestreleaser.towncrier  # integrate towncrier into zest.releaser

-r requirements-tools.txt
