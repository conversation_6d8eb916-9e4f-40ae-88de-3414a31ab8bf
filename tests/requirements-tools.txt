# This is the pip requirements file for running the
# PyInstaller test-suite.
#
# For extensive testing you will also need to install what is defined in
# requirements-libraries.txt.
#
# Example (assuming current dir is PyInstaller's top-level source dir)::
#
#   python -m pip install -r tests/requirements-tools.txt
#   python -m pip install -r tests/requirements-libraries.txt  # extensive

# include PyInstaller's requirements
-r ../requirements.txt

# Work-around for a bug in execnet 1.4.1
execnet >= 1.5.0

# Testing framework.
pytest >= 2.7.3

# Plugin allowing running tests in parallel.
pytest-xdist
# Plugin to abort hanging tests.
pytest-timeout
# allows specifying order without duplicates
pytest-drop-dup-tests

# Better subprocess alternative with implemented timeout.
psutil

# Check new flake8 violations on pull requests
flake8-diff

subprocess32; python_version == '2.7'

# Current pypiwin32 simply installs pywin32. Only an old pypiwin32 release has a
# Python 3.4 wheel.
pywin32; python_version != '3.4' and sys_platform == 'win32'
pypiwin32==219; python_version == '3.4' and sys_platform == 'win32'
