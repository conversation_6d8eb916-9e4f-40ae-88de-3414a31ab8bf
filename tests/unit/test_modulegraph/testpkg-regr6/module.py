
ds = {
    'name': [
        list(1)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
        + list(2)
    ]
}

import os
