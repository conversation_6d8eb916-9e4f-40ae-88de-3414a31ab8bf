# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(2, 0, 4, 0),
    prodvers=(2, 0, 4, 0),
    # Contains a bitmask that specifies the valid bits 'flags'
    mask=0x0,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904E4',
        [StringStruct(u'CompanyName', u'VideoLAN'),
        StringStruct(u'ProductName', u'VLC media player'),
        StringStruct(u'ProductVersion', u'2,0,4,0'),
        StringStruct(u'InternalName', u'vlc'),
        StringStruct(u'OriginalFilename', u'vlc.exe'),
        StringStruct(u'FileVersion', u'2.0.4'),
        StringStruct(u'FileDescription', u'VLC media player 2.0.4'),
        StringStruct(u'LegalCopyright', u'Copyright © 1996-2012 VideoLAN and VLC Authors'),
        StringStruct(u'LegalTrademarks', u'VLC media player, VideoLAN and x264 are registered trademarks from VideoLAN')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1252])])
  ]
)
