License
=======

|PyInstaller| is distributed under the `GPL License`_ but with
an exception that allows you to use it to build commercial products:

 #. You may use PyInstaller to bundle commercial applications out of your
    source code.

 #. The executable bundles generated by PyInstaller from your source code
    can be shipped with whatever license you want.

 #. You may modify PyInstaller for your own needs but changes to the
    PyInstaller source code fall under the terms of the GPL license.
    That is, if you distribute your modifications you must distribute
    them under GPL terms.

For updated information or clarification see our
`FAQ`_ at the `PyInstaller`_ home page.


.. include:: _common_definitions.txt

.. Emacs config:
 Local Variables:
 mode: rst
 ispell-local-dictionary: "american"
 End:
