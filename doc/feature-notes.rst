.. _feature notes:

===============================
Notes about specific Features
===============================

This sections describes details about specific features. For a
:ref:`full list of features <website:features>`
please refer to the website.


Ctypes Dependencies
=========================

Ctypes is a foreign function library for Python, that allows calling functions
present in shared libraries. Those libraries are not imported as Python
packages, because they are not picked up via Python imports: their path is
passed to ctypes instead, which deals with the shared library directly; this
caused <1.4 PyInstaller import detect machinery to miss those libraries,
failing the goal to build self-contained PyInstaller executables::

  from ctypes import *
  # This will pass undetected under PyInstaller detect machinery,
  # because it's not a direct import.
  handle = CDLL("/usr/lib/library.so")
  handle.function_call()


Solution in |PyInstaller|
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

PyInstaller contains a pragmatic implementation of Ctypes dependencies: it
will search for simple standard usages of ctypes and **automatically** track
and bundle the referenced libraries. The following usages will be correctly
detected::

  CDLL("library.so")
  WinDLL("library.so")
  ctypes.DLL("library.so")
  cdll.library # Only valid under Windows - a limitation of ctypes, not PyInstaller's
  windll.library # Only valid under Windows - a limitation of ctypes, not PyInstaller's
  cdll.LoadLibrary("library.so")
  windll.LoadLibrary("library.so")


More in detail, the following restrictions apply:

* **only libraries referenced by bare filenames (e.g. no leading paths) will
  be handled**; handling absolute paths would be impossible without modifying
  the bytecode as well (remember that while running frozen, ctypes would keep
  searching the library at that very absolute location, whose presence on the
  host system nobody can guarantee), and relative paths handling would require
  recreating in the frozen executable the same hierarchy of directories
  leading to the library, in addition of keeping track of which the current
  working directory is;

* **only library paths represented by a literal string will be detected and
  included in the final executable**: PyInstaller import detection works by
  inspecting raw Python bytecode, and since you can pass the library path to
  ctypes using a string (that can be represented by a literal in the code, but
  also by a variable, by the return value of an arbitrarily complex function,
  etc...), it's not reasonably possible to detect **all** ctypes dependencies;

* **only libraries referenced in the same context of ctypes' invocation will
  be handled**.

We feel that it should be enough to cover most ctypes' usages, with little or
no modification required in your code.

If |PyInstaller| does not detect a library, you can add it to your
bundle by passing the respective information to ``--add-binary`` option or
:ref:`listing it in the .spec-file <adding binary files>`. If your frozen
application will be able to pick up the library at run-time can not be
guaranteed as it depends on the detailed implementation.


Gotchas
~~~~~~~~~~~~~~~

The ctypes detection system at :ref:`Analysis time <spec-file operations>`
is based on ``ctypes.util.find_library()``.
This means that you have to make sure
that while performing ``Analysis`` and running frozen,
all the environment values ``find_library()`` uses to search libraries
are aligned to those when running un-frozen.
Examples include using ``LD_LIBRARY_PATH`` or ``DYLD_LIBRARY_PATH`` to
widen ``find_library()`` scope.


SWIG support
=========================

|PyInstaller| tries to detect binary modules created by SWIG. This detection
requires:

- The Python wrapper module must be imported somewhere in your application
  (or by any of the modules it uses).

- The wrapper module must be available as source-code and it's first line must
  contain the text ``automatically generated by SWIG``.

- The C-module must have the same name as the wrapper module prefixed with an
  underscore (``_``). (This is a SWIG restriction already.)

- The C-module must sit just beside the wrapper module (thus a relative import
  would work).

Also some restrictions apply for Python 3, due to the way the SWIG wrapper is
implemented:

- The C-module will become a `global` module. As a consequence, you can not
  use two SWIG modules with the same basename (e.g. ``pkg1._cmod`` and
  ``pkg2._cmod``), as one would overwrite the other.


Cython support
======================

|PyInstaller| can follow import statements that refer to Cython C object
modules and bundle them – like for any other module implemented in C.

But – again, as for any other module implemented in C – |PyInstaller| can not
determine if the Cython C object module is importing some Python module.
These will typically show up as in a traceback like this
(mind the ``.pyx`` extension)::

    Traceback (most recent call last):
    […]
    File "myapp\cython_module.pyx", line 3, in init myapp.cython_module
    ModuleNotFoundError: No module named 'csv'

So if you are using a Cython C object module, which imports Python modules,
you will have to list these as ``--hidden-import``.


.. include:: _common_definitions.txt

.. Emacs config:
 Local Variables:
 mode: rst
 ispell-local-dictionary: "american"
 End:
