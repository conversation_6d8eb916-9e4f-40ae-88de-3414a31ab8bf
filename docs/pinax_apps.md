# Pinax Apps

Every Pinax app has documentation in it's README.md file. We recommend you follow
instructions found there for installing and adding a Pinax app to your project.

## Current Distribution (18.01) Pinax Apps

### [django-user-accounts](https://github.com/pinax/django-user-accounts)
django-user-accounts provides a Django project with a very extensible infrastructure for dealing with user accounts.

### [pinax-announcements](https://github.com/pinax/pinax-announcements)
pinax-announcements is a site-wide announcement reusable app for Django.

### [pinax-blog](https://github.com/pinax/pinax-blog)
pinax-blog is a blog app for Django.

### [pinax-calendars](https://github.com/pinax/pinax-calendars)
pinax-calendars provides utilities for publishing events as a calendar.

### [pinax-eventlog](https://github.com/pinax/pinax-eventlog)
pinax-eventlog is a simple app that provides an easy and clean interface for logging diagnostic
as well as business intelligence data about activity that occurs in your site.

### [pinax-events](https://github.com/pinax/pinax-events)
pinax-events is a simple app for publishing events on your site.

### [pinax-images](https://github.com/pinax/pinax-images)
pinax-images is an app for managing collections of images associated with any content object.

### [pinax-invitations](https://github.com/pinax/pinax-invitations)
pinax-invitations is a site invitation app for Django.

### [pinax-likes](https://github.com/pinax/pinax-likes)
pinax-likes is a liking app for Django.

### [pinax-messages](https://github.com/pinax/pinax-messages)
pinax-messages is an app for providing private user-to-user threaded messaging.

### [pinax-news](https://github.com/pinax/pinax-news)
pinax-news is a simple app for publishing links to news articles on your site.

### [pinax-notifications](https://github.com/pinax/pinax-notifications)
pinax-notifications is a user notification management app for the Django web framework.

### [pinax-patches](https://github.com/pinax/pinax-patches)

### [pinax-stripe](https://github.com/pinax/pinax-stripe)
pinax-stripe is a payments Django app for Stripe.
It allows you to process one off charges as well as signup users for recurring subscriptions managed by Stripe.

### [pinax-testimonials](https://github.com/pinax/pinax-testimonials)
pinax-testimonials provides support for any site wanting to display testimonials.

### [pinax-waitinglist](https://github.com/pinax/pinax-waitinglist)
django-waitinglist is a Django waiting list app for running a private beta with cohorts support.

### [pinax-webanalytics](https://github.com/pinax/pinax-webanalytics)
pinax-webanalytics provides analytics and metrics integration for Django.


## Updated Pinax Apps

These apps are not part of the latest official distribution, but have been updated
to support the latest Django/Python compatibility matrix, including Django 2.0.

Their omission may be because the app is not demonstrated with a starter or demo project,
insufficient documentation, or lack of thorough tests.

However, just because an app is not included in the Pinax distribution does not mean
it is bad or useless. Many of these additional apps are useful as-is, and are sourced
for various real-world projects.
 
If you'd like to help improve the Pinax distribution list, we appreciate Pinax app
pull requests to improve documentation, fix issues, and add tests.

### [pinax-badges](https://github.com/pinax/pinax-badges)
pinax-badges allows awarding badges to users in Django.

### [pinax-cohorts](https://github.com/pinax/pinax-cohorts)
pinax-cohorts allows creating cohorts for inviting people off your pinax-waitinglist
waiting list to your private beta site.

### [pinax-comments](https://github.com/pinax/pinax-comments)
pinax-comments is a comments app for Django.

### [pinax-documents](https://github.com/pinax/pinax-documents)
pinax-documents is a document management app for collecting and sharing documents in folders.

### [pinax-forums](https://github.com/pinax/pinax-forums)
pinax-forums is an extensible forums app for Django and Pinax.

### [pinax-points](https://github.com/pinax/pinax-points)
pinax-points is a points, positions, and levels app for Django.

### [pinax-ratings](https://github.com/pinax/pinax-ratings)
pinax-ratings is a ratings app for Django.

### [pinax-referrals](https://github.com/pinax/pinax-referrals)
pinax-referrals provides a Django site with referrals functionality.

### [pinax-submissions](https://github.com/pinax/pinax-submissions)
pinax-submissions is an app for proposing and reviewing submissions.

### [pinax-teams](https://github.com/pinax/pinax-teams)
pinax-teams is an app for Django sites that supports open, by invitation, and by application teams.

### [pinax-wiki](https://github.com/pinax/pinax-wiki)
pinax-wiki lets you easily add a wiki to your Django site.


## Other Pinax Apps

### [django-bookmarks](https://github.com/pinax/django-bookmarks)
django-bookmarks provides bookmark management for the Django web framework.

### [django-mailer](https://github.com/pinax/django-mailer)
django-mailer is a reusable Django app for queuing the sending of email.

### [django-flag](https://github.com/pinax/django-flag)
django-flag provides flagging of inappropriate spam/content.

### [django-friends](https://github.com/pinax/django-friends)
django-friends provides friendship, contact, and invitation management for the Django web framework.

### [pinax-lms-activities](https://github.com/pinax/pinax-lms-activities)
pinax-lms-activities provides a framework and base learning activities for Pinax LMS.

### [pinax-phone-confirmation](https://github.com/pinax/pinax-phone-confirmation)
pinax-phone-confirmation is an app to provide phone confirmation via Twilio.

### [pinax-types](https://github.com/pinax/pinax-types)

### [symposion](https://github.com/pinax/symposion)
symposion is a conference management solution from Eldarion. It was built with the generous support of the Python Software Foundation. See http://eldarion.com/symposion/ for commercial support, customization and hosting.


## Support and Demonstration Apps

### [cloudspotting2](https://github.com/pinax/cloudspotting2)
Demonstration project showing the use of many Pinax apps. Clone and run this project locally to see
the Pinax apps in action.

### [pinax-calendars-demo](https://github.com/pinax/pinax-calendars-demo)
Clone and run this project to see pinax-calendars app in action.

### [pinax-design](https://github.com/pinax/pinax-design)
Source for Pinax app patches, demonstrated at http://pinaxproject.com/pinax-design/.

### [pinax-starter-app](https://github.com/pinax/pinax-starter-app)
pinax-starter-app contains starter app templates used to create base projects from which you build your site.

### [pinax-templates](https://github.com/pinax/pinax-templates)
Provides default template for many Pinax apps.

### [pinax_theme_tester](https://github.com/pinax/pinax_theme_tester)
pinax_theme_tester provides the magic driving the template demonstration site http://templates.pinaxproject.com.
