.. _introduction:

Introduction
============

.. image:: https://farm5.staticflickr.com/4317/35198386374_1939af3de6_k_d.jpg

Philosophy
----------

Requests was developed with a few :pep:`20` idioms in mind.


#. Beautiful is better than ugly.
#. Explicit is better than implicit.
#. Simple is better than complex.
#. Complex is better than complicated.
#. Readability counts.

All contributions to Requests should keep these important rules in mind.

.. _`apache2`:

Apache2 License
---------------

A large number of open source projects you find today are `GPL Licensed`_.
While the GPL has its time and place, it should most certainly not be your
go-to license for your next open source project.

A project that is released as GPL cannot be used in any commercial product
without the product itself also being offered as open source.

The MIT, BSD, ISC, and Apache2 licenses are great alternatives to the GPL
that allow your open-source software to be used freely in proprietary,
closed-source software.

Requests is released under terms of `Apache2 License`_.

.. _`GPL Licensed`: https://opensource.org/licenses/gpl-license.php
.. _`Apache2 License`: https://opensource.org/licenses/Apache-2.0


Requests License
----------------

    .. include:: ../../LICENSE
