#
# The content of this file will be filled in with meaningful data
# when creating an archive using `git archive` or by downloading an
# archive from github, e.g. from github.com/.../archive/develop.zip
#
rev = "$Format:%h$"     # abbreviated commit hash
commit = "$Format:%H$"  # commit hash
date = "$Format:%ci$"   # commit date
author = "$Format:%an$ <$Format:%ae$>"
ref_names = "$Format:%D$"  # incl. current branch
commit_message = """$Format:%B$"""
