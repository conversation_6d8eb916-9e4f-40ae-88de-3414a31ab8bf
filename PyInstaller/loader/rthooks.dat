{
    'django':     ['pyi_rth_django.py'],
    'enchant':    ['pyi_rth_enchant.py'],
    'gi':         ['pyi_rth_gi.py'],
    'gi.repository.Gio':    ['pyi_rth_gio.py'],
    'gi.repository.GLib':   ['pyi_rth_glib.py'],
    'gi.repository.GdkPixbuf':    ['pyi_rth_gdkpixbuf.py'],
    'gi.repository.Gtk':    ['pyi_rth_gtk.py'],
    'gi.repository.Gst':    ['pyi_rth_gstreamer.py'],
    'gst':        ['pyi_rth_gstreamer.py'],
    'kivy':       ['pyi_rth_kivy.py'],
    'kivy.lib.gstplayer': ['pyi_rth_gstreamer.py'],
    'matplotlib': ['pyi_rth_mplconfig.py', 'pyi_rth_mpldata.py'],
    'osgeo':      ['pyi_rth_osgeo.py'],
    'pkg_resources':  ['pyi_rth_pkgres.py'],
    'PyQt4':      ['pyi_rth_qt4plugins.py'],
    'PyQt5':      ['pyi_rth_qt5.py'],
    'PyQt5.QtWebEngineWidgets': ['pyi_rth_qt5webengine.py'],
    'PySide':      ['pyi_rth_qt4plugins.py'],
    'PySide2':      ['pyi_rth_qt5plugins.py'],
    'PySide2.QtQuick':  ['pyi_rth_qml.py'],
    'PySide2.QtWebEngineWidgets': ['pyi_rth_qt5webengine.py'],
    '_tkinter':    ['pyi_rth__tkinter.py'],
    'traitlets':  ['pyi_rth_traitlets.py'],
    'twisted.internet.reactor':        ['pyi_rth_twisted.py'],
    'usb':        ['pyi_rth_usb.py'],
    'win32com':   ['pyi_rth_win32comgenpy.py'],
    'multiprocessing': ['pyi_rth_multiprocessing.py'],
    'nltk': ['pyi_rth_nltk.py'],
}
