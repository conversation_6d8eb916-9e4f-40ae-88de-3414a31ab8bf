.\" Man page generated from reStructuredText.
.
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.TH "PIPENV" "1" "May 02, 2025" "2025.0.2" "pipenv"
.sp
\fBNOTE:\fP
.INDENT 0.0
.INDENT 3.5
This guide is written for Python 3.7+
.UNINDENT
.UNINDENT
.SH MAKE SURE YOU HAVE PYTHON AND PIP
.sp
Before you go any further, make sure you have Python and that it’s available
from your command line. You can check this by simply running
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ python \-\-version

.EE
.UNINDENT
.UNINDENT
.sp
You should get some output like \fB3.12.1\fP\&. If you do not have Python, please
install the latest 3.x version from \X'tty: link https://python.org'\fI\%python.org\fP\X'tty: link'
.sp
Additionally, make sure you have \X'tty: link https://pypi.org/project/pip/'\fI\%pip\fP\X'tty: link' available, assuming you install via pip, our preferred method of installation.
Check this by running
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pip \-\-version
pip 24.0

.EE
.UNINDENT
.UNINDENT
.sp
If you installed Python from source, with an installer from \X'tty: link https://python.org'\fI\%python.org\fP\X'tty: link' or via \X'tty: link https://brew.sh/'\fI\%Homebrew\fP\X'tty: link', you likely already have pip.
If you’re on Linux and installed using your OS package manager, you may have to \X'tty: link https://pip.pypa.io/en/stable/installing/'\fI\%install pip\fP\X'tty: link' manually.
.SH INSTALLING PIPENV
.SS Preferred Installation of Pipenv
.sp
It is recommended that users on most platforms install pipenv from \X'tty: link https://pypi.org'\fI\%pypi.org\fP\X'tty: link' using
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pip install pipenv \-\-user

.EE
.UNINDENT
.UNINDENT
.sp
\fBNOTE:\fP
.INDENT 0.0
.INDENT 3.5
pip \X'tty: link https://pip.pypa.io/en/stable/user_guide/#user-installs'\fI\%user installations\fP\X'tty: link' allow for installation into your home directory to prevent breaking any system\-wide packages.
Due to interaction between dependencies, you should limit tools installed in this way to basic building blocks for a Python workflow such as virtualenv, pipenv, tox, and similar software.
.UNINDENT
.UNINDENT
.sp
If \fBpipenv\fP isn’t available in your shell after installation,
you’ll need to add the user site\-packages binary directory to your \fBPATH\fP\&.
.sp
On Linux and macOS you can find the \X'tty: link https://docs.python.org/3/library/site.html#site.USER_BASE'\fI\%user base\fP\X'tty: link' binary directory by running
\fBpython \-m site \-\-user\-base\fP and appending \fBbin\fP to the end. For example,
this will typically print \fB~/.local\fP (with \fB~\fP expanded to the
absolute path to your home directory), so you’ll need to add
\fB~/.local/bin\fP to your \fBPATH\fP\&. You can set your \fBPATH\fP permanently by
\X'tty: link https://stackoverflow.com/a/14638025'\fI\%modifying ~/.profile\fP\X'tty: link'\&.
.sp
On Windows you can find the user base binary directory by running
\fBpython \-m site \-\-user\-site\fP and replacing \fBsite\-packages\fP with
\fBScripts\fP\&. For example, this could return
\fBC:\eUsers\eUsername\eAppData\eRoaming\ePython37\esite\-packages\fP, so you would
need to set your \fBPATH\fP to include
\fBC:\eUsers\eUsername\eAppData\eRoaming\ePython37\eScripts\fP\&. You can set your
user \fBPATH\fP permanently in the \X'tty: link https://learn.microsoft.com/en-us/windows/win32/shell/user-environment-variables'\fI\%Control Panel\fP\X'tty: link'\&.
.sp
You may need to log out for the \fBPATH\fP changes to take effect.
.sp
To upgrade pipenv at any time:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pip install \-\-user \-\-upgrade pipenv

.EE
.UNINDENT
.UNINDENT
.SS Homebrew Installation of Pipenv
.INDENT 0.0
.IP \(bu 2
\X'tty: link https://brew.sh/'\fI\%Homebrew\fP\X'tty: link' is a popular open\-source package management system for macOS (or Linux).
.UNINDENT
.sp
Once you have installed Homebrew simply run
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ brew install pipenv

.EE
.UNINDENT
.UNINDENT
.sp
To upgrade pipenv at any time:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ brew upgrade pipenv

.EE
.UNINDENT
.UNINDENT
.sp
\fBNOTE:\fP
.INDENT 0.0
.INDENT 3.5
Homebrew installation is discouraged because it works better to install pipenv using pip on macOS.
.UNINDENT
.UNINDENT
.SH INSTALLING PACKAGES FOR YOUR PROJECT
.sp
Pipenv manages dependencies on a per\-project basis. To install a package,
change into your project’s directory (or just an empty directory for this
tutorial) and run
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ cd myproject
$ pipenv install <package>

.EE
.UNINDENT
.UNINDENT
.sp
\fBNOTE:\fP
.INDENT 0.0
.INDENT 3.5
Pipenv is designed to be used by non\-privileged OS users. It is not meant
to install or handle packages for the whole OS. Running Pipenv as \fBroot\fP
or with \fBsudo\fP (or \fBAdmin\fP on Windows) is highly discouraged and might
lead to unintend breakage of your OS.
.UNINDENT
.UNINDENT
.sp
Pipenv will install the package and create a \fBPipfile\fP
for you in your project’s directory. The \fBPipfile\fP is used to track which
dependencies your project needs in case you need to re\-install them, such as
when you share your project with others.
.sp
For example when installing the \fBrequests\fP library, you should get output similar to this:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pipenv install requests
Creating a virtualenv for this project...
Pipfile: /home/<USER>/pipenv\-triage/test_install2/Pipfile
Using default python from /mnt/extra/miniconda3/bin/python (3.12.1) to create virtualenv...
⠹ Creating virtual environment...created virtual environment CPython3.12.1.final.0\-64 in 139ms
  creator CPython3Posix(dest=/home/<USER>/Envs/test_install2\-DMnDbAT9, clear=False, no_vcs_ignore=False, global=False)
  seeder FromAppData(download=False, pip=bundle, via=copy, app_data_dir=/home/<USER>/.local/share/virtualenv)
    added seed packages: pip==24.0
  activators BashActivator,CShellActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator

✔ Successfully created virtual environment!
Virtualenv location: /home/<USER>/Envs/test_install2\-DMnDbAT9
Creating a Pipfile for this project...
Installing requests...
Resolving requests...
Added requests to Pipfile\(aqs [packages] ...
✔ Installation Succeeded
Pipfile.lock not found, creating...
Locking [packages] dependencies...
Building requirements...
Resolving dependencies...
✔ Success!
Locking [dev\-packages] dependencies...
Updated Pipfile.lock (1977acb1ba9778abb66054090e2618a0a1f1759b1b3b32afd8a7d404ba18b4fb)!
To activate this project\(aqs virtualenv, run pipenv shell.
Alternatively, run a command inside the virtualenv with pipenv run.
Installing dependencies from Pipfile.lock (18b4fb)...

.EE
.UNINDENT
.UNINDENT
.SH USING INSTALLED PACKAGES
.sp
Now that \fBrequests\fP is installed you can create a simple \fBmain.py\fP file to use it:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
import requests

response = requests.get(\(aqhttps://httpbin.org/ip\(aq)
print(\(aqYour IP is {0}\(aq.format(response.json()[\(aqorigin\(aq]))

.EE
.UNINDENT
.UNINDENT
.sp
Then you can run this script using \fBpipenv run\fP
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pipenv run python main.py

.EE
.UNINDENT
.UNINDENT
.sp
You should get output similar to this:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
Your IP is *******

.EE
.UNINDENT
.UNINDENT
.sp
Using \fB$ pipenv run\fP ensures that your installed packages are available to
your script by activating the virtualenv. It is also possible to spawn a new shell
that ensures all commands have access to your installed packages with \fB$ pipenv shell\fP\&.
.SH VIRTUALENV MAPPING CAVEAT
.INDENT 0.0
.IP \(bu 2
Pipenv automatically maps projects to their specific virtualenvs.
.IP \(bu 2
By default, the virtualenv is stored globally with the name of the project’s root directory plus the hash of the full path to the project’s root (e.g., \fBmy_project\-a3de50\fP).
.IP \(bu 2
Should you change your project’s path, you break such a default mapping and pipenv will no longer be able to find and to use the project’s virtualenv.
.IP \(bu 2
If you must move or rename a directory managed by pipenv, run ‘pipenv \-\-rm’ before renaming or moving your project directory. Then, after renaming or moving the directory run ‘pipenv install’ to recreate the virtualenv.
.IP \(bu 2
Customize this behavior with \fBPIPENV_CUSTOM_VENV_NAME\fP environment variable.
.IP \(bu 2
You might also prefer to set \fBPIPENV_VENV_IN_PROJECT=1\fP in your .env or .bashrc/.zshrc (or other shell configuration file) for creating the virtualenv inside your project’s directory.
.UNINDENT
.SH AUTHOR
Python Packaging Authority
.SH COPYRIGHT
2020. A project founded by Kenneth Reitz and maintained by <a href="https://www.pypa.io/en/latest/">Python Packaging Authority (PyPA).</a>
.\" Generated by docutils manpage writer.
.
