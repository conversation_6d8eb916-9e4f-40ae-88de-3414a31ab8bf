{"version": 1, "projects": {"account": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/account", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": ["https://github.com/pinax/pinax-starter-projects/archive/account-1.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-1.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-1.1.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-1.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-1.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-1.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-1.4.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-2.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-2.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-2.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-2.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-2.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-2.4.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-3.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-4.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-4.0.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-4.0.2.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-4.0.3.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/account-4.0.4.tar.gz"]}, "blog": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/blog", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": ["https://github.com/pinax/pinax-starter-projects/archive/blog-1.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-1.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-1.1.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-1.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-1.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-1.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-1.4.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-2.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-2.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-2.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-2.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-2.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-2.5.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-2.5.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-3.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-4.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-4.0.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-4.0.2.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-4.0.3.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/blog-4.0.4.tar.gz"]}, "company": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/company", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": ["https://github.com/pinax/pinax-starter-projects/archive/company-0.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/company-0.1.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/company-1.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/company-2.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/company-2.0.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/company-2.0.2.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/company-2.0.3.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/company-2.0.4.tar.gz"]}, "documents": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/documents", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": []}, "social-auth": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/social-auth", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": []}, "static": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/static", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": ["https://github.com/pinax/pinax-starter-projects/archive/static-1.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-1.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-1.1.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-1.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-1.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-1.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-1.4.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-2.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-2.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-2.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-2.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-2.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-2.4.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-3.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-4.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-4.0.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-4.0.2.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-4.0.3.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/static-4.0.4.tar.gz"]}, "stripe": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/stripe", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": ["https://github.com/pinax/pinax-starter-projects/archive/stripe-1.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-1.0.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-2.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-2.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-2.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-2.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-2.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-2.4.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-3.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-4.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-4.0.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-4.0.2.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-4.0.3.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/stripe-4.0.4.tar.gz"]}, "team-wiki": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/team-wiki", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": []}, "waitinglist": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/waitinglist", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": ["https://github.com/pinax/pinax-starter-projects/archive/waitinglist-1.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/waitinglist-1.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/waitinglist-1.1.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/waitinglist-2.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/waitinglist-3.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/waitinglist-3.0.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/waitinglist-3.0.2.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/waitinglist-3.0.3.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/waitinglist-3.0.4.tar.gz"], "instructions": "You need to replace the background image."}, "wiki": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/wiki", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": []}, "zero": {"url": "https://github.com/pinax/pinax-starter-projects/zipball/zero", "process-files": ["index.js", "README.md", "requirements-zero.txt"], "releases": ["https://github.com/pinax/pinax-starter-projects/archive/zero-1.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-1.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-1.1.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-1.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-1.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-1.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-1.4.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-2.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-2.1.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-2.2.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-2.3.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-2.4.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-2.4.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-3.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-4.0.0.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-4.0.1.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-4.0.2.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-4.0.3.tar.gz", "https://github.com/pinax/pinax-starter-projects/archive/zero-4.0.4.tar.gz"]}}}