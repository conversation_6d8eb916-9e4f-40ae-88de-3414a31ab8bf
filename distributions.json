{"16.04": {"demos": {"cloudspotting": "1.0.1", "pinax-calendars-demo": "1.0.1"}, "projects": {"account": "2.3.0", "blog": "2.4.0", "static": "2.3.0", "stripe": "2.3.0", "waitinglist": "1.0.0", "zero": "2.3.0"}, "apps": {"django-user-accounts": "1.3.1", "pinax-announcements": "2.0.3", "pinax-blog": "5.0.0", "pinax-calendars": "1.0.0", "pinax-eventlog": "1.1.1", "pinax-images": "1.0.0", "pinax-likes": "2.0.4", "pinax-stripe": "3.1.0", "pinax-testimonials": "1.0.3", "pinax-waitinglist": "1.1.1", "pinax-webanalytics": "2.0.1"}, "tools": {"pinax-cli": "1.0.0"}, "themes": {"pinax-theme-bootstrap": "7.8.0"}}, "16.07": {"demos": {"cloudspotting": "1.1.0", "pinax-calendars-demo": "1.1.0"}, "projects": {"account": "2.4.1", "blog": "2.5.1", "company": "0.1.1", "static": "2.4.1", "stripe": "2.4.1", "waitinglist": "1.1.1", "zero": "2.4.1"}, "apps": {"django-user-accounts": "1.3.1", "pinax-announcements": "2.0.4", "pinax-blog": "5.0.2", "pinax-calendars": "1.1.0", "pinax-eventlog": "1.1.2", "pinax-events": "1.0.0", "pinax-images": "2.0.0", "pinax-likes": "2.0.4", "pinax-messages": "1.0.1", "pinax-news": "1.0.0", "pinax-stripe": "3.2.1", "pinax-testimonials": "1.0.5", "pinax-waitinglist": "1.1.1", "pinax-webanalytics": "2.0.4"}, "tools": {"pinax-cli": "1.0.0"}, "themes": {"pinax-theme-bootstrap": "7.10.1"}}, "18.01": {"demos": {"cloudspotting2": "1.0.0", "pinax-calendars-demo": "1.1.0"}, "projects": {"account": "4.0.3", "blog": "4.0.3", "company": "2.0.3", "static": "4.0.3", "stripe": "4.0.3", "waitinglist": "3.0.3", "zero": "4.0.3"}, "apps": {"django-user-accounts": "2.0.3", "pinax-announcements": "3.0.2", "pinax-blog": "7.0.1", "pinax-calendars": "2.0.4", "pinax-eventlog": "2.0.3", "pinax-events": "2.0.3", "pinax-images": "3.0.1", "pinax-invitations": "6.1.2", "pinax-likes": "3.0.1", "pinax-messages": "2.0.2", "pinax-news": "2.0.3", "pinax-notifications": "5.0.3", "pinax-stripe": "4.0.0", "pinax-testimonials": "2.0.4", "pinax-waitinglist": "2.0.3", "pinax-webanalytics": "4.0.2"}, "design": {"pinax-design": "1.0.0", "pinax-templates": "1.0.4"}, "tools": {"pinax-cli": "1.1.0"}, "themes": {"pinax-theme-bootstrap": "8.0.1"}}}